services:
  # Redis for caching and real-time data
  redis:
    image: redis:7-alpine
    container_name: mev-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # PostgreSQL for persistent data backup
  postgres:
    image: postgres:15-alpine
    container_name: mev-postgres
    environment:
      POSTGRES_DB: mev_arbitrage_bot
      POSTGRES_USER: mev_user
      POSTGRES_PASSWORD: mev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/postgres-backup-schema.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mev_user -d mev_arbitrage_bot"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # InfluxDB for time-series data
  influxdb:
    image: influxdb:2.7-alpine
    container_name: mev-influxdb
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: Simm40
      DOCKER_INFLUXDB_INIT_PASSWORD: 40ayamkingofTyre!
      DOCKER_INFLUXDB_INIT_ORG: Dev-KE
      DOCKER_INFLUXDB_INIT_BUCKET: mev-monitoring
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: 0yplGOzAMMkssXmfL_kD40Ey78LZ8_De2RbvK5Z-s-bIMZPbU_F2k-ZB3_jo-wpUbPZhsf3VDcgGwu3jwy5Ctw==
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "influx", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 60s

  # Backend service
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: mev-backend
    environment:
      - NODE_ENV=production
      - PORT=3001
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=************************************************/mev_arbitrage_bot
      - INFLUXDB_URL=http://influxdb:8086
    ports:
      - "3001:3001"
    depends_on:
      - redis
      - postgres
      - influxdb
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  # Frontend service
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: mev-frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    restart: unless-stopped

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: mev-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
  influxdb_data:
